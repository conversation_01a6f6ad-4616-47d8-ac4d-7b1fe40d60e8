package setting

import (
	"encoding/json"
	"fmt"
	"one-api/common"
	"sync"
)

// 分销配置结构
type CommissionConfig struct {
	Enabled            bool     `json:"enabled"`               // 是否启用分销
	FirstLevelRate     float64  `json:"first_level_rate"`      // 一级分销比例
	SecondLevelRate    float64  `json:"second_level_rate"`     // 二级分销比例
	MinWithdrawAmount  int      `json:"min_withdraw_amount"`   // 最小提现金额（额度）
	WithdrawEnabled    bool     `json:"withdraw_enabled"`      // 是否开启提现
	AutoSettle         bool     `json:"auto_settle"`           // 是否自动结算
	PromotionWhitelist []string `json:"promotion_whitelist"`   // 推广页面白名单用户列表
	PromotionOpenToAll bool     `json:"promotion_open_to_all"` // 推广页面是否对所有用户开放
}

var (
	commissionConfig = CommissionConfig{
		Enabled:            false,
		FirstLevelRate:     0.5,   // 默认50%
		SecondLevelRate:    0.2,   // 默认20%
		MinWithdrawAmount:  10000, // 默认最小提现10000额度
		WithdrawEnabled:    true,
		AutoSettle:         true,
		PromotionWhitelist: []string{},
		PromotionOpenToAll: false,
	}
	commissionConfigMutex sync.RWMutex
)

// GetCommissionConfig 获取分销配置副本
func GetCommissionConfig() CommissionConfig {
	commissionConfigMutex.RLock()
	defer commissionConfigMutex.RUnlock()

	// 创建副本
	configCopy := commissionConfig
	configCopy.PromotionWhitelist = make([]string, len(commissionConfig.PromotionWhitelist))
	copy(configCopy.PromotionWhitelist, commissionConfig.PromotionWhitelist)

	return configCopy
}

// UpdateCommissionConfig 更新分销配置
func UpdateCommissionConfig(config CommissionConfig) error {
	commissionConfigMutex.Lock()
	defer commissionConfigMutex.Unlock()

	// 验证配置
	if config.FirstLevelRate < 0 || config.FirstLevelRate > 1 {
		return fmt.Errorf("一级分销比例必须在0-1之间")
	}
	if config.SecondLevelRate < 0 || config.SecondLevelRate > 1 {
		return fmt.Errorf("二级分销比例必须在0-1之间")
	}
	if config.FirstLevelRate+config.SecondLevelRate > 1 {
		return fmt.Errorf("一级和二级分销比例之和不能超过100%%")
	}
	if config.MinWithdrawAmount < 0 {
		return fmt.Errorf("最小提现金额不能为负")
	}

	commissionConfig = config
	return nil
}

// CommissionConfig2JSONString 将分销配置转为JSON字符串
func CommissionConfig2JSONString() string {
	commissionConfigMutex.RLock()
	defer commissionConfigMutex.RUnlock()

	jsonBytes, err := json.Marshal(commissionConfig)
	if err != nil {
		common.SysError("error marshalling commission config: " + err.Error())
		return "{}"
	}
	return string(jsonBytes)
}

// UpdateCommissionConfigByJSONString 通过JSON字符串更新分销配置
func UpdateCommissionConfigByJSONString(jsonStr string) error {
	var config CommissionConfig
	err := json.Unmarshal([]byte(jsonStr), &config)
	if err != nil {
		return err
	}
	return UpdateCommissionConfig(config)
}

// IsCommissionEnabled 检查分销是否启用
func IsCommissionEnabled() bool {
	commissionConfigMutex.RLock()
	defer commissionConfigMutex.RUnlock()
	return commissionConfig.Enabled
}

// GetCommissionRates 获取分销比例
func GetCommissionRates() (firstLevel, secondLevel float64) {
	commissionConfigMutex.RLock()
	defer commissionConfigMutex.RUnlock()
	return commissionConfig.FirstLevelRate, commissionConfig.SecondLevelRate
}

// GetMinWithdrawAmount 获取最小提现金额
func GetMinWithdrawAmount() int {
	commissionConfigMutex.RLock()
	defer commissionConfigMutex.RUnlock()
	return commissionConfig.MinWithdrawAmount
}

// IsWithdrawEnabled 检查提现是否启用
func IsWithdrawEnabled() bool {
	commissionConfigMutex.RLock()
	defer commissionConfigMutex.RUnlock()
	return commissionConfig.WithdrawEnabled
}

// IsAutoSettle 检查是否自动结算
func IsAutoSettle() bool {
	commissionConfigMutex.RLock()
	defer commissionConfigMutex.RUnlock()
	return commissionConfig.AutoSettle
}

// IsUserAllowedPromotion 检查用户是否可以访问推广页面
func IsUserAllowedPromotion(username string) bool {
	commissionConfigMutex.RLock()
	defer commissionConfigMutex.RUnlock()

	if commissionConfig.PromotionOpenToAll {
		return true
	}

	for _, allowedUser := range commissionConfig.PromotionWhitelist {
		if allowedUser == username {
			return true
		}
	}

	return false
}
