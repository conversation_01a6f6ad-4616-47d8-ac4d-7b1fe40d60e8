#!/bin/bash

# 设置变量
IMAGE_NAME="lddff/new-api"
# 使用时间戳作为版本号
IMAGE_TAG=$(date +"%Y%m%d%H%M%S")
REMOTE_HOST="root@************"
REMOTE_PATH="/"
ARCHIVE_NAME="new-api-docker.tar"
# 默认不跳过构建
SKIP_BUILD=false

# 解析命令行参数
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --skip-build) SKIP_BUILD=true ;;
        *) echo "未知参数: $1"; exit 1 ;;
    esac
    shift
done

# 显示彩色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

# 错误处理函数
handle_error() {
    echo -e "${RED}错误: $1${NC}"
    exit 1
}

# 显示执行步骤
echo -e "${YELLOW}===== 开始一键部署流程 =====${NC}"

# 1. 构建Docker镜像 (除非跳过)
if [ "$SKIP_BUILD" = false ]; then
    echo -e "${YELLOW}当前构建版本: ${IMAGE_TAG} (时间戳)${NC}"
    echo -e "${GREEN}[1/4] 构建Docker镜像 ${IMAGE_NAME}:${IMAGE_TAG}${NC}"
    docker build -t ${IMAGE_NAME}:${IMAGE_TAG} . || handle_error "Docker构建失败"
    # 同时打上latest标签
    docker tag ${IMAGE_NAME}:${IMAGE_TAG} ${IMAGE_NAME}:latest
    echo -e "${GREEN}[2/4] 保存Docker镜像到本地文件 ${ARCHIVE_NAME}${NC}"
    docker save ${IMAGE_NAME}:${IMAGE_TAG} -o ${ARCHIVE_NAME} || handle_error "保存Docker镜像失败"
else
    echo -e "${YELLOW}跳过构建步骤，直接使用最新镜像 ${IMAGE_NAME}:latest${NC}"
    echo -e "${GREEN}[1/3] 保存Docker镜像到本地文件 ${ARCHIVE_NAME}${NC}"
    docker save ${IMAGE_NAME}:latest -o ${ARCHIVE_NAME} || handle_error "保存Docker镜像失败"
fi

# 压缩Docker镜像文件
if [ "$SKIP_BUILD" = false ]; then
    echo -e "${GREEN}[3/4] 压缩Docker镜像文件${NC}"
else
    echo -e "${GREEN}[2/3] 压缩Docker镜像文件${NC}"
fi
gzip -f ${ARCHIVE_NAME} || handle_error "压缩Docker镜像失败"

# 将压缩包上传到远程服务器（改用 rsync）
if [ "$SKIP_BUILD" = false ]; then
    echo -e "${GREEN}[4/4] 上传压缩包到远程服务器 ${REMOTE_HOST}:${REMOTE_PATH}${NC}"
else
    echo -e "${GREEN}[3/3] 上传压缩包到远程服务器 ${REMOTE_HOST}:${REMOTE_PATH}${NC}"
fi
rsync -avP ${ARCHIVE_NAME}.gz ${REMOTE_HOST}:${REMOTE_PATH} || handle_error "上传到远程服务器失败"

# 清理本地文件
echo -e "${GREEN}清理本地临时文件${NC}"
rm ${ARCHIVE_NAME}.gz

echo -e "${GREEN}===== 部署完成! =====${NC}"
echo -e "${YELLOW}镜像已上传至 ${REMOTE_HOST}:${REMOTE_PATH}${ARCHIVE_NAME}.gz${NC}"
echo -e "${YELLOW}在远程服务器上，您可以使用以下命令加载镜像:${NC}"
echo -e "${GREEN}gunzip -c ${REMOTE_PATH}${ARCHIVE_NAME}.gz | docker load${NC}"
echo -e "${GREEN}docker run -d --name new-api -p 3000:3000 ${IMAGE_NAME}:latest${NC}"

# 在远程服务器上执行makedocker脚本
echo -e "${YELLOW}===== 在远程服务器执行makedocker脚本 =====${NC}"
ssh ${REMOTE_HOST} "cd / && ./makedocker" || handle_error "在远程服务器执行makedocker脚本失败"
echo -e "${GREEN}makedocker脚本执行完成!${NC}"

exit 0
