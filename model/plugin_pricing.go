package model

import (
	"encoding/json"
	"errors"
)

// PluginPrice 插件价格模型
type PluginPrice struct {
	Id        int    `json:"id" gorm:"primaryKey"`
	Key       string `json:"key" gorm:"type:varchar(255);uniqueIndex"`
	NameEn    string `json:"name_en" gorm:"type:varchar(255)"`
	NameZh    string `json:"name_zh" gorm:"type:varchar(255)"`
	Unit      string `json:"unit" gorm:"type:varchar(50)"`
	Price     string `json:"price" gorm:"type:varchar(50)"`
	PriceDesc string `json:"price_desc" gorm:"type:varchar(255);default:''"`
	Disabled  bool   `json:"disabled" gorm:"default:false"`
	CreatedAt int64  `json:"created_at" gorm:"autoCreateTime:milli"`
	UpdatedAt int64  `json:"updated_at" gorm:"autoUpdateTime:milli"`
}

// TableName 指定表名
func (PluginPrice) TableName() string {
	return "plugin_prices"
}

// GetAllPluginPrices 获取所有插件价格数据
func GetAllPluginPrices() ([]PluginPrice, error) {
	var prices []PluginPrice
	err := DB.Where("disabled = ?", false).Find(&prices).Error
	return prices, err
}

// GetPluginPriceByKey 根据Key获取插件价格
func GetPluginPriceByKey(key string) (*PluginPrice, error) {
	var price PluginPrice
	err := DB.Where("key = ?", key).First(&price).Error
	if err != nil {
		return nil, err
	}
	return &price, nil
}

// GetPluginPriceById 根据ID获取插件价格
func GetPluginPriceById(id int) (*PluginPrice, error) {
	var price PluginPrice
	err := DB.Where("id = ?", id).First(&price).Error
	if err != nil {
		return nil, err
	}
	return &price, nil
}

// InsertPluginPrice 插入新的插件价格
func InsertPluginPrice(price *PluginPrice) error {
	// 检查key是否已存在
	var count int64
	DB.Model(&PluginPrice{}).Where("key = ?", price.Key).Count(&count)
	if count > 0 {
		return errors.New("插件key已存在")
	}
	return DB.Create(price).Error
}

// UpdatePluginPrice 更新插件价格
func UpdatePluginPrice(price *PluginPrice) error {
	return DB.Save(price).Error
}

// DeletePluginPrice 删除插件价格
func DeletePluginPrice(id int) error {
	return DB.Unscoped().Delete(&PluginPrice{}, id).Error
}

// InitialPluginPrices 初始化默认的插件价格
func InitialPluginPrices() {
	// 默认的插件价格数据
	defaultPrices := []PluginPrice{
		{Key: "bilibili_search", NameEn: "bilibili_search", NameZh: "B站搜索", Unit: "次", Price: "0.03"},
		{Key: "xhs_search", NameEn: "xhs_search", NameZh: "小红书搜索", Unit: "次", Price: "0.03"},
		{Key: "get_video_content_from_download", NameEn: "get_video_content_from_download", NameZh: "语音识别文案", Unit: "次", Price: "0.05"},
		{Key: "dy_user_profile_vedio", NameEn: "dy_user_profile_vedio", NameZh: "抖音主页", Unit: "次", Price: "0.03"},
		{Key: "dy_search_video", NameEn: "dy_search_video", NameZh: "抖音搜索", Unit: "次", Price: "0.03"},
		{Key: "parse_video", NameEn: "parse_video", NameZh: "全平台无水印下载视频、图集", Unit: "次", Price: "0.01"},
		{Key: "fix_content", NameEn: "fix_content", NameZh: "修复文案", Unit: "单位", Price: "0.05"},
	}

	// 检查表是否为空
	var count int64
	DB.Model(&PluginPrice{}).Count(&count)
	if count == 0 {
		// 插入默认数据
		for _, price := range defaultPrices {
			DB.Create(&price)
		}
	}
}

// MigratePluginPrices 数据库迁移
func MigratePluginPrices() {
	// 如果表不存在，创建表
	if !DB.Migrator().HasTable(&PluginPrice{}) {
		err := DB.Migrator().CreateTable(&PluginPrice{})
		if err != nil {
			panic(err)
		}
	} else {
		// 如果表已存在，更新表结构
		err := DB.AutoMigrate(&PluginPrice{})
		if err != nil {
			panic(err)
		}
	}
}

// ExportPluginPrices 导出插件价格为JSON
func ExportPluginPrices() (string, error) {
	prices, err := GetAllPluginPrices()
	if err != nil {
		return "", err
	}
	jsonData, err := json.Marshal(prices)
	if err != nil {
		return "", err
	}
	return string(jsonData), nil
}

// ImportPluginPrices 从JSON导入插件价格
func ImportPluginPrices(jsonData string) error {
	var prices []PluginPrice
	err := json.Unmarshal([]byte(jsonData), &prices)
	if err != nil {
		return err
	}

	// 清空现有数据
	err = DB.Exec("DELETE FROM plugin_prices").Error
	if err != nil {
		return err
	}

	// 插入新数据
	for _, price := range prices {
		err = DB.Create(&price).Error
		if err != nil {
			return err
		}
	}
	return nil
}
