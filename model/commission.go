package model

import (
	"fmt"
	"one-api/common"

	"gorm.io/gorm"
)

// Commission 佣金记录表
type Commission struct {
	Id             int     `json:"id" gorm:"primaryKey"`
	UserId         int     `json:"user_id" gorm:"index;comment:获得佣金的用户ID"`
	FromUserId     int     `json:"from_user_id" gorm:"index;comment:产生佣金的用户ID"`
	OrderUserId    int     `json:"order_user_id" gorm:"comment:实际消费的用户ID"`
	Type           string  `json:"type" gorm:"type:varchar(20);comment:佣金类型：first_level, second_level"`
	OrderType      string  `json:"order_type" gorm:"type:varchar(20);comment:订单类型：topup, consume"`
	OrderAmount    int     `json:"order_amount" gorm:"comment:订单金额（额度）"`
	CommissionRate float64 `json:"commission_rate" gorm:"comment:佣金比例"`
	Amount         int     `json:"amount" gorm:"comment:佣金金额（额度）"`
	Status         string  `json:"status" gorm:"type:varchar(20);default:'pending';comment:状态：pending, settled, withdrawn"`
	Remark         string  `json:"remark" gorm:"type:text;comment:备注"`
	CreatedAt      int64   `json:"created_at" gorm:"comment:创建时间"`
	SettledAt      int64   `json:"settled_at" gorm:"comment:结算时间"`
}

// Withdrawal 提现申请表
type Withdrawal struct {
	Id              int            `json:"id" gorm:"primaryKey"`
	UserId          int            `json:"user_id" gorm:"index;comment:提现用户ID"`
	Amount          int            `json:"amount" gorm:"comment:提现金额（额度）"`
	RealAmount      float64        `json:"real_amount" gorm:"comment:实际提现金额（元）"`
	Status          string         `json:"status" gorm:"type:varchar(20);default:'pending';comment:状态：pending, approved, rejected, paid"`
	PaymentMethod   string         `json:"payment_method" gorm:"type:varchar(50);comment:支付方式"`
	PaymentAccount  string         `json:"payment_account" gorm:"type:varchar(255);comment:收款账户"`
	PaymentRealName string         `json:"payment_real_name" gorm:"type:varchar(50);comment:收款人真实姓名"`
	PaymentContact  string         `json:"payment_contact" gorm:"type:varchar(50);comment:联系方式"`
	OrderNo         string         `json:"order_no" gorm:"type:varchar(50);unique;comment:订单号"`
	Remark          string         `json:"remark" gorm:"type:text;comment:备注"`
	AdminRemark     string         `json:"admin_remark" gorm:"type:text;comment:管理员备注"`
	CreatedAt       int64          `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt       int64          `json:"updated_at" gorm:"comment:更新时间"`
	ApprovedAt      int64          `json:"approved_at" gorm:"comment:审核时间"`
	PaidAt          int64          `json:"paid_at" gorm:"comment:支付时间"`
	DeletedAt       gorm.DeletedAt `gorm:"index"`
}

// GetUserTotalCommission 获取用户总佣金
func GetUserTotalCommission(userId int) (int, error) {
	var total int64
	err := DB.Model(&Commission{}).
		Where("user_id = ? AND status = ?", userId, "settled").
		Select("COALESCE(SUM(amount), 0)").
		Scan(&total).Error
	return int(total), err
}

// GetUserPendingCommission 获取用户待结算佣金
func GetUserPendingCommission(userId int) (int, error) {
	var total int64
	err := DB.Model(&Commission{}).
		Where("user_id = ? AND status = ?", userId, "pending").
		Select("COALESCE(SUM(amount), 0)").
		Scan(&total).Error
	return int(total), err
}

// GetUserWithdrawnCommission 获取用户已提现佣金
func GetUserWithdrawnCommission(userId int) (int, error) {
	var total int64
	err := DB.Model(&Commission{}).
		Where("user_id = ? AND status = ?", userId, "withdrawn").
		Select("COALESCE(SUM(amount), 0)").
		Scan(&total).Error
	return int(total), err
}

// GetUserAvailableCommission 获取用户可提现佣金
func GetUserAvailableCommission(userId int) (int, error) {
	settled, err := GetUserTotalCommission(userId)
	if err != nil {
		return 0, err
	}
	withdrawn, err := GetUserWithdrawnCommission(userId)
	if err != nil {
		return 0, err
	}
	return settled - withdrawn, nil
}

// CreateCommission 创建佣金记录
func CreateCommission(commission *Commission) error {
	return DB.Create(commission).Error
}

// CreateWithdrawal 创建提现申请
func CreateWithdrawal(withdrawal *Withdrawal) error {
	return DB.Create(withdrawal).Error
}

// GetWithdrawalByOrderNo 根据订单号获取提现申请
func GetWithdrawalByOrderNo(orderNo string) (*Withdrawal, error) {
	var withdrawal Withdrawal
	err := DB.Where("order_no = ?", orderNo).First(&withdrawal).Error
	return &withdrawal, err
}

// UpdateWithdrawalStatus 更新提现状态
func UpdateWithdrawalStatus(id int, status string, adminRemark string) error {
	updates := map[string]interface{}{
		"status":       status,
		"admin_remark": adminRemark,
		"updated_at":   common.GetTimestamp(),
	}
	if status == "approved" {
		updates["approved_at"] = common.GetTimestamp()
	} else if status == "paid" {
		updates["paid_at"] = common.GetTimestamp()
	}
	return DB.Model(&Withdrawal{}).Where("id = ?", id).Updates(updates).Error
}

// GetUserCommissions 获取用户佣金明细
func GetUserCommissions(userId int, offset, limit int) ([]*Commission, int64, error) {
	var commissions []*Commission
	var total int64

	err := DB.Model(&Commission{}).Where("user_id = ?", userId).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = DB.Where("user_id = ?", userId).
		Order("id desc").
		Offset(offset).
		Limit(limit).
		Find(&commissions).Error

	return commissions, total, err
}

// GetUserWithdrawals 获取用户提现记录
func GetUserWithdrawals(userId int, offset, limit int) ([]*Withdrawal, int64, error) {
	var withdrawals []*Withdrawal
	var total int64

	err := DB.Model(&Withdrawal{}).Where("user_id = ?", userId).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = DB.Where("user_id = ?", userId).
		Order("id desc").
		Offset(offset).
		Limit(limit).
		Find(&withdrawals).Error

	return withdrawals, total, err
}

// GetPendingWithdrawals 获取待审核的提现申请
func GetPendingWithdrawals(offset, limit int) ([]*Withdrawal, int64, error) {
	var withdrawals []*Withdrawal
	var total int64

	err := DB.Model(&Withdrawal{}).Where("status = ?", "pending").Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = DB.Where("status = ?", "pending").
		Order("id asc").
		Offset(offset).
		Limit(limit).
		Find(&withdrawals).Error

	return withdrawals, total, err
}

// MarkCommissionsAsWithdrawn 将指定金额的佣金标记为已提现
func MarkCommissionsAsWithdrawn(tx *gorm.DB, userId int, amount int) error {
	// 1. 查询所有已结算的佣金记录，按时间排序
	// 使用 FOR UPDATE 行锁来防止并发修改
	var commissions []Commission
	err := tx.Where("user_id = ? AND status = ?", userId, "settled").
		Order("id asc").
		Set("gorm:query_option", "FOR UPDATE").
		Find(&commissions).Error
	if err != nil {
		return err
	}

	// 2. 累计金额，标记需要更新的记录
	var totalMarked int
	var idsToUpdate []int
	for _, commission := range commissions {
		if totalMarked >= amount {
			break
		}
		totalMarked += commission.Amount
		idsToUpdate = append(idsToUpdate, commission.Id)
	}

	// 3. 检查金额是否足够
	if totalMarked < amount {
		return fmt.Errorf("已结算佣金不足，需要%d，实际只有%d", amount, totalMarked)
	}

	// 4. 批量更新状态
	if len(idsToUpdate) > 0 {
		err = tx.Model(&Commission{}).
			Where("id IN ?", idsToUpdate).
			Update("status", "withdrawn").Error
		if err != nil {
			return err
		}
	}

	return nil
}
