package service

import (
	"fmt"
	"one-api/common"
	"one-api/model"
	"one-api/setting"
	"time"
)

// ProcessCommission 处理分销佣金
// orderType: "topup" 或 "consume"
// userId: 产生订单的用户ID
// amount: 订单金额（额度）
func ProcessCommission(orderType string, userId int, amount int) error {
	// 使用defer recover确保不会因为panic影响主流程
	defer func() {
		if r := recover(); r != nil {
			common.SysError(fmt.Sprintf("ProcessCommission panic recovered: %v", r))
		}
	}()

	// 检查分销是否启用
	if !setting.IsCommissionEnabled() {
		return nil
	}

	// 获取用户信息
	user, err := model.GetUserById(userId, false)
	if err != nil {
		return err
	}

	// 新增逻辑：只有当消费用户（user）已设置 InviteeGroup 时才允许向上分润
	if user.InviteeGroup == "" {
		return nil
	}

	// 如果没有邀请人，不产生佣金
	if user.InviterId == 0 {
		return nil
	}

	// 预先获取配置中的比例
	cfgFirstRate, cfgSecondRate := setting.GetCommissionRates()

	// ------------- 校验各级邀请关系是否满足分组要求 -------------
	// 一级邀请人
	inviter, err := model.GetUserById(user.InviterId, false)
	if err != nil {
		inviter = nil // 查询失败视为无效
	}
	firstLevelValid := false
	if inviter != nil && inviter.InviteeGroup != "" && inviter.InviteeGroup == user.Group {
		firstLevelValid = true
	}

	// 二级邀请人（在一级有效的前提下继续向上寻找）
	var secondInviter *model.User
	secondLevelValid := false
	if inviter != nil {
		secondInviter, _ = model.GetUserById(inviter.InviterId, false)
		if secondInviter != nil && secondInviter.InviteeGroup != "" && secondInviter.InviteeGroup == user.Group && cfgSecondRate > 0 {
			secondLevelValid = true
		}
	}

	// 如果两个层级若有不符合条件，则直接返回
	if !firstLevelValid || !secondLevelValid {
		return nil
	}

	// 根据是否存在二级分润动态调整一级分润比例：
	//  - 仅一级时：一级 = cfgFirstRate（50%）
	//  - 同时有二级时：一级 = cfgFirstRate - cfgSecondRate（如 0.5-0.2=0.3）
	firstLevelRate := 0.0
	secondLevelRate := 0.0
	if secondLevelValid {
		secondLevelRate = cfgSecondRate
		firstLevelRate = cfgFirstRate - cfgSecondRate
		if firstLevelRate < 0 {
			firstLevelRate = 0
		}
	} else if firstLevelValid {
		firstLevelRate = cfgFirstRate
	}

	// ------------- 生成分润记录 -------------
	// 如果一级有效，生成一级分润记录
	if firstLevelValid && firstLevelRate > 0 {
		firstLevelAmount := int(float64(amount) * firstLevelRate)
		if firstLevelAmount > 0 {
			commission := &model.Commission{
				UserId:         inviter.Id,
				FromUserId:     userId,
				OrderUserId:    userId,
				Type:           "first_level",
				OrderType:      orderType,
				OrderAmount:    amount,
				CommissionRate: firstLevelRate,
				Amount:         firstLevelAmount,
				Status:         "pending",
				Remark:         fmt.Sprintf("来自用户 %s 的%s订单", user.Username, getOrderTypeName(orderType)),
				CreatedAt:      time.Now().Unix(),
			}

			if err := model.CreateCommission(commission); err != nil {
				common.SysError(fmt.Sprintf("创建一级佣金记录失败: %v", err))
			}

			if setting.IsAutoSettle() {
				settleCommission(commission)
			}
		}
	}

	// 如果二级有效，生成二级分润记录
	if secondLevelValid && secondLevelRate > 0 {
		secondLevelAmount := int(float64(amount) * secondLevelRate)
		if secondLevelAmount > 0 {
			commission := &model.Commission{
				UserId:         secondInviter.Id,
				FromUserId:     inviter.Id,
				OrderUserId:    userId,
				Type:           "second_level",
				OrderType:      orderType,
				OrderAmount:    amount,
				CommissionRate: secondLevelRate,
				Amount:         secondLevelAmount,
				Status:         "pending",
				Remark:         fmt.Sprintf("来自用户 %s 的%s订单（二级）", user.Username, getOrderTypeName(orderType)),
				CreatedAt:      time.Now().Unix(),
			}

			if err := model.CreateCommission(commission); err != nil {
				common.SysError(fmt.Sprintf("创建二级佣金记录失败: %v", err))
			}

			if setting.IsAutoSettle() {
				settleCommission(commission)
			}
		}
	}

	return nil
}

// settleCommission 结算佣金
func settleCommission(commission *model.Commission) {
	if commission.Status != "pending" {
		return
	}

	// 更新佣金状态为已结算
	commission.Status = "settled"
	commission.SettledAt = time.Now().Unix()

	err := model.DB.Save(commission).Error
	if err != nil {
		common.SysError(fmt.Sprintf("结算佣金失败: %v", err))
		return
	}

	// 记录日志
	model.RecordLog(commission.UserId, model.LogTypeSystem,
		fmt.Sprintf("获得%s佣金 %s", getCommissionTypeName(commission.Type), common.LogQuota(commission.Amount)))
}

// getOrderTypeName 获取订单类型名称
func getOrderTypeName(orderType string) string {
	switch orderType {
	case "topup":
		return "充值"
	case "consume":
		return "消费"
	default:
		return orderType
	}
}

// getCommissionTypeName 获取佣金类型名称
func getCommissionTypeName(commissionType string) string {
	switch commissionType {
	case "first_level":
		return "一级分销"
	case "second_level":
		return "二级分销"
	default:
		return commissionType
	}
}

// GetCommissionStatistics 获取用户佣金统计
func GetCommissionStatistics(userId int) (map[string]interface{}, error) {
	totalCommission, err := model.GetUserTotalCommission(userId)
	if err != nil {
		return nil, err
	}

	pendingCommission, err := model.GetUserPendingCommission(userId)
	if err != nil {
		return nil, err
	}

	withdrawnCommission, err := model.GetUserWithdrawnCommission(userId)
	if err != nil {
		return nil, err
	}

	availableCommission, err := model.GetUserAvailableCommission(userId)
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"total":     totalCommission,
		"pending":   pendingCommission,
		"withdrawn": withdrawnCommission,
		"available": availableCommission,
	}, nil
}

// GetCommissionDetails 获取佣金明细统计
func GetCommissionDetails(userId int) (map[string]interface{}, error) {
	// 获取一级下线统计
	var firstLevelStats struct {
		Count  int64
		Amount int64
	}
	err := model.DB.Model(&model.Commission{}).
		Where("user_id = ? AND type = ? AND status = ?", userId, "first_level", "settled").
		Select("COUNT(DISTINCT from_user_id) as count, COALESCE(SUM(amount), 0) as amount").
		Scan(&firstLevelStats).Error
	if err != nil {
		return nil, err
	}

	// 获取二级下线统计
	var secondLevelStats struct {
		Count  int64
		Amount int64
	}
	err = model.DB.Model(&model.Commission{}).
		Where("user_id = ? AND type = ? AND status = ?", userId, "second_level", "settled").
		Select("COUNT(DISTINCT order_user_id) as count, COALESCE(SUM(amount), 0) as amount").
		Scan(&secondLevelStats).Error
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"first_level": map[string]interface{}{
			"count":  firstLevelStats.Count,
			"amount": int(firstLevelStats.Amount),
		},
		"second_level": map[string]interface{}{
			"count":  secondLevelStats.Count,
			"amount": int(secondLevelStats.Amount),
		},
	}, nil
}
