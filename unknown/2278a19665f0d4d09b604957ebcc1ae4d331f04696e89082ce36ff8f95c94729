name: Linux Release
permissions:
  contents: write

on:
  push:
    tags:
      - '*'
      - '!*-alpha*'
jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - name: Build Frontend
        env:
          CI: ""
        run: |
          cd web
          npm install
          REACT_APP_VERSION=$(git describe --tags) npm run build
          cd ..
      - name: Set up Go
        uses: actions/setup-go@v3
        with:
          go-version: '>=1.18.0'
      - name: Build Backend (amd64)
        run: |
          go mod download
          go build -ldflags "-s -w -X 'one-api/common.Version=$(git describe --tags)' -extldflags '-static'" -o one-api

      - name: Build Backend (arm64)
        run: |
          sudo apt-get update
          DEBIAN_FRONTEND=noninteractive sudo apt-get install -y gcc-aarch64-linux-gnu
          CC=aarch64-linux-gnu-gcc CGO_ENABLED=1 GOOS=linux GOARCH=arm64 go build -ldflags "-s -w -X 'one-api/common.Version=$(git describe --tags)' -extldflags '-static'" -o one-api-arm64

      - name: Release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: |
            one-api
            one-api-arm64
          draft: true
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}