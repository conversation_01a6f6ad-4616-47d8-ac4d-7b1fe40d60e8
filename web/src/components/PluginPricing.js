import React, { useEffect, useState } from 'react';
import { Card, Table, Typography, Button, Modal, Input, Popconfirm, Space, Upload, Badge, Tooltip } from '@douyinfe/semi-ui';
import { useTranslation } from 'react-i18next';
import { API, isAdmin, showError, showSuccess } from '../helpers';
import { IconDelete, IconEdit, IconPlus, IconImport, IconExport, IconTickCircle, IconClear, IconAlertTriangle } from '@douyinfe/semi-icons';

const { Title, Text } = Typography;

const PluginPricing = () => {
  const { t } = useTranslation();
  const [pricingData, setPricingData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentPrice, setCurrentPrice] = useState(null);
  const [isAdminUser, setIsAdminUser] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importData, setImportData] = useState('');
  const [exporting, setExporting] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [verifyResults, setVerifyResults] = useState([]);
  const [verifyModalVisible, setVerifyModalVisible] = useState(false);
  
  // 简单的表单状态管
  const [formData, setFormData] = useState({
    key: '',
    name_en: '',
    name_zh: '',
    unit: '',
    price: '',
    price_desc: ''
  });
  const [formErrors, setFormErrors] = useState({});

  const fetchData = async () => {
    try {
      setLoading(true);
      const res = await API.get('/api/plugin-pricing');
      if (res.data.success) {
        setPricingData(res.data.data);
      } else {
        showError(res.data.message);
      }
    } catch (error) {
      showError(error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    setIsAdminUser(isAdmin());
    fetchData();
  }, []);

  const handleInputChange = (field, value) => {
    setFormData({
      ...formData,
      [field]: value
    });
    
    // 清除该字段的错误
    if (formErrors[field]) {
      setFormErrors({
        ...formErrors,
        [field]: null
      });
    }
  };

  const validateForm = () => {
    const errors = {};
    if (!formData.key) errors.key = '请输入插件标识';
    if (!formData.name_en) errors.name_en = '请输入英文名称';
    if (!formData.name_zh) errors.name_zh = '请输入中文名称';
    if (!formData.unit) errors.unit = '请输入计量单位';
    if (!formData.price) {
      errors.price = '请输入价格';
    } else if (!/^(([1-9]\d*)|0)(\.\d{1,2})?$/.test(formData.price)) {
      errors.price = '请输入正确的价格格式';
    }
    if (!formData.price_desc) errors.price_desc = '请输入价格描述';
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const resetForm = () => {
    setFormData({
      key: '',
      name_en: '',
      name_zh: '',
      unit: '',
      price: '',
      price_desc: ''
    });
    setFormErrors({});
  };

  const handleAddOrEdit = async () => {
    if (!validateForm()) return;
    
    try {
      let res;
      if (currentPrice) {
        res = await API.put(`/api/plugin-pricing/${currentPrice.id}`, formData);
      } else {
        res = await API.post('/api/plugin-pricing', formData);
      }
      
      if (res.data.success) {
        showSuccess(res.data.message || '操作成功');
        setModalVisible(false);
        resetForm();
        fetchData();
      } else {
        showError(res.data.message);
      }
    } catch (error) {
      showError(error.message);
    }
  };

  const handleDelete = async (id) => {
    try {
      const res = await API.delete(`/api/plugin-pricing/${id}`);
      if (res.data.success) {
        showSuccess(res.data.message || '删除成功');
        fetchData();
      } else {
        showError(res.data.message);
      }
    } catch (error) {
      showError(error.message);
    }
  };

  const handleVerify = async () => {
    try {
      setVerifying(true);
      const res = await API.get('/api/plugin-pricing/verify');
      if (res.data.success) {
        setVerifyResults(res.data.data);
        setVerifyModalVisible(true);
        showSuccess('校验完成');
      } else {
        showError(res.data.message);
      }
    } catch (error) {
      showError(error.message);
    } finally {
      setVerifying(false);
    }
  };

  const openEditModal = (record) => {
    setCurrentPrice(record);
    setFormData({
      key: record.key,
      name_en: record.name_en,
      name_zh: record.name_zh,
      unit: record.unit,
      price: record.price,
      price_desc: record.price_desc || ''
    });
    setFormErrors({});
    setModalVisible(true);
  };

  const openAddModal = () => {
    setCurrentPrice(null);
    resetForm();
    setModalVisible(true);
  };

  const handleExport = async () => {
    try {
      setExporting(true);
      const res = await API.get('/api/plugin-pricing/export');
      if (res.data.success) {
        const jsonStr = JSON.stringify(JSON.parse(res.data.data), null, 2);
        const blob = new Blob([jsonStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'plugin_pricing_data.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        showSuccess('导出成功');
      } else {
        showError(res.data.message);
      }
    } catch (error) {
      showError(error.message);
    } finally {
      setExporting(false);
    }
  };

  const handleImport = async () => {
    try {
      if (!importData) {
        showError('请输入有效的JSON数据');
        return;
      }
      
      const res = await API.post('/api/plugin-pricing/import', {
        json_data: importData
      });
      
      if (res.data.success) {
        showSuccess(res.data.message || '导入成功');
        setImportModalVisible(false);
        setImportData('');
        fetchData();
      } else {
        showError(res.data.message);
      }
    } catch (error) {
      showError(error.message);
    }
  };

  const uploadChange = info => {
    if (info.file.status === 'error') {
      showError(`${info.file.name} 文件上传失败`);
    }
    
    if (info.file.status === 'done') {
      const reader = new FileReader();
      reader.onload = e => {
        try {
          setImportData(e.target.result);
        } catch (error) {
          showError('文件解析失败，请确保上传的是有效的JSON文件');
        }
      };
      reader.readAsText(info.file.fileInstance);
    }
  };

  const adminColumns = [
    {
      title: t('插件名称'),
      dataIndex: 'name',
      render: (text, record) => (
        <div>
          <Text strong>{record.name_zh}</Text>
          <br />
          <Text type="tertiary" size="small">{record.name_en}</Text>
        </div>
      ),
      width: '40%'
    },
    {
      title: t('插件标识'),
      dataIndex: 'key',
      width: '10%'
    },
    {
      title: t('计量单位'),
      dataIndex: 'unit',
      width: '10%',
      className: 'tableHiddle'
    },
    {
      title: t('价格¥'),
      dataIndex: 'price',
      width: '10%',
      className: 'tableHiddle'
    },
    {
      title: t('价格描述'),
      dataIndex: 'price_desc',
      width: '20%'
    },
    {
      title: t('操作'),
      dataIndex: 'operation',
      width: '20%',
      render: (text, record) => (
        <Space>
          <Button icon={<IconEdit />} onClick={() => openEditModal(record)} size="small" theme="light" type="primary">
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个插件价格吗？"
            content="此操作不可撤销"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button icon={<IconDelete />} size="small" type="danger" theme="light">
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  const publicColumns = [
    {
      title: t('插件名称'),
      dataIndex: 'name',
      render: (text, record) => (
        <div>
          <Text strong>{record.name_zh}</Text>
          <br />
          <Text type="tertiary" size="small">{record.name_en}</Text>
        </div>
      ),
      width: '50%'
    },
    {
      title: t('计量单位'),
      dataIndex: 'unit',
      width: '15%',
      className: 'tableHiddle'
    },
    {
      title: t('价格¥'),
      dataIndex: 'price',
      width: '15%',
      className: 'tableHiddle'
    },
    {
      title: t('价格描述'),
      dataIndex: 'price_desc',
      width: '20%'
    }
  ];

  const verifyResultColumns = [
    {
      title: t('插件名称'),
      dataIndex: 'name',
      width: '15%',
    },
    {
      title: t('插件标识'),
      dataIndex: 'key',
      width: '15%',
    },
    {
      title: t('计量单位'),
      dataIndex: 'unit',
      width: '10%',
      className: 'tableHiddle',
    },
    {
      title: t('表格价格'),
      dataIndex: 'plugin_price',
      width: '15%',
      className: 'tableHiddle',
      render: (text, record) => {
        if (record.token_price && record.token_price !== "") {
          if (record.plugin_price && record.plugin_price !== "0") {
            return (
              <div>
                <div>{record.plugin_price}</div>
                <div style={{ color: 'var(--semi-color-text-2)', fontSize: '12px' }}>{record.token_price}</div>
              </div>
            );
          } else {
            return record.token_price;
          }
        }
        return text;
      }
    },
    {
      title: t('真实价格'),
      dataIndex: 'real_price',
      width: '15%',
      render: (text) => (
        <div style={{ color: 'var(--semi-color-success)' }}>{text}</div>
      )
    },
    {
      title: t('价格描述'),
      dataIndex: 'plugin_price_desc',
      width: '30%',
    }
  ];

  return (
    <div style={{ padding: '20px' }}>
      <Card
        bordered={false}
        headerLine={false}
        title={
          <Title heading={3} style={{ margin: '8px 0' }}>
            {t('计费标准')}
          </Title>
        }
        style={{ borderRadius: '8px' }}
        headerExtraContent={
          isAdminUser && (
            <Space>
              <Button icon={<IconTickCircle />} onClick={handleVerify} loading={verifying} type="secondary">
                校验价格
              </Button>
              <Button icon={<IconPlus />} onClick={openAddModal} type="primary">
                添加
              </Button>
              <Button icon={<IconImport />} onClick={() => setImportModalVisible(true)}>
                导入
              </Button>
              <Button icon={<IconExport />} onClick={handleExport} loading={exporting}>
                导出
              </Button>
            </Space>
          )
        }
      >
        <Text style={{ fontSize: '16px', display: 'block', marginBottom: '24px' }}>
          {t('以下是各插件的计费标准')}
        </Text>
        
        <Table
          columns={isAdminUser ? adminColumns : publicColumns}
          dataSource={pricingData}
          pagination={false}
          size="large"
          loading={loading}
          rowKey="id"
          style={{ 
            marginTop: '16px',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
            borderRadius: '8px',
            overflow: 'hidden'
          }}
        />
      </Card>
      
      <Modal
        title={currentPrice ? '编辑插件价格' : '添加插件价格'}
        visible={modalVisible}
        onOk={handleAddOrEdit}
        onCancel={() => setModalVisible(false)}
        centered
        width={600}
        okText="确定"
        cancelText="取消"
      >
        <div style={{ padding: '16px 0' }}>
          <div style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>插件标识:</label>
            <Input
              value={formData.key}
              onChange={(value) => handleInputChange('key', value)}
              placeholder="请输入插件唯一标识"
              disabled={currentPrice !== null}
            />
            {formErrors.key && <div style={{ color: 'red', fontSize: '12px', marginTop: '4px' }}>{formErrors.key}</div>}
          </div>
          
          <div style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>英文名称:</label>
            <Input
              value={formData.name_en}
              onChange={(value) => handleInputChange('name_en', value)}
              placeholder="请输入插件英文名称"
            />
            {formErrors.name_en && <div style={{ color: 'red', fontSize: '12px', marginTop: '4px' }}>{formErrors.name_en}</div>}
          </div>
          
          <div style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>中文名称:</label>
            <Input
              value={formData.name_zh}
              onChange={(value) => handleInputChange('name_zh', value)}
              placeholder="请输入插件中文名称"
            />
            {formErrors.name_zh && <div style={{ color: 'red', fontSize: '12px', marginTop: '4px' }}>{formErrors.name_zh}</div>}
          </div>
          
          <div style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>计量单位:</label>
            <Input
              value={formData.unit}
              onChange={(value) => handleInputChange('unit', value)}
              placeholder="请输入计量单位"
            />
            {formErrors.unit && <div style={{ color: 'red', fontSize: '12px', marginTop: '4px' }}>{formErrors.unit}</div>}
          </div>
          
          <div style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>价格:</label>
            <Input
              value={formData.price}
              onChange={(value) => handleInputChange('price', value)}
              placeholder="请输入价格"
            />
            {formErrors.price && <div style={{ color: 'red', fontSize: '12px', marginTop: '4px' }}>{formErrors.price}</div>}
          </div>
          
          <div style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>价格描述:</label>
            <Input
              value={formData.price_desc}
              onChange={(value) => handleInputChange('price_desc', value)}
              placeholder="请输入价格描述"
            />
            {formErrors.price_desc && <div style={{ color: 'red', fontSize: '12px', marginTop: '4px' }}>{formErrors.price_desc}</div>}
          </div>
        </div>
      </Modal>
      
      <Modal
        title="导入插件价格数据"
        visible={importModalVisible}
        onOk={handleImport}
        onCancel={() => {
          setImportModalVisible(false);
          setImportData('');
        }}
        centered
        width={600}
        okText="导入"
        cancelText="取消"
      >
        <div style={{ marginBottom: 16 }}>
          <Upload
            action="/"
            accept=".json"
            showUploadList={false}
            customRequest={({ onSuccess }) => onSuccess({ status: 'done' })}
            onChange={uploadChange}
          >
            <Button icon={<IconImport />}>选择JSON文件</Button>
          </Upload>
        </div>
        <Input
          type="textarea"
          value={importData}
          onChange={setImportData}
          placeholder="请粘贴JSON格式的数据或选择文件上传"
          rows={10}
        />
      </Modal>

      <Modal
        title="插件价格校验结果"
        visible={verifyModalVisible}
        onCancel={() => setVerifyModalVisible(false)}
        centered
        width={1200}
        footer={
          <Button type="primary" onClick={() => setVerifyModalVisible(false)}>关闭</Button>
        }
      >
        <Table
          columns={verifyResultColumns}
          dataSource={verifyResults}
          pagination={false}
          size="middle"
          rowKey="key"
        />
      </Modal>
    </div>
  );
};

export default PluginPricing; 
