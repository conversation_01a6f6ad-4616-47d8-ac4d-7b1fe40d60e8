import React, { useEffect, useState } from 'react';
import {
  API,
  copy,
  showError,
  showSuccess,
  timestamp2string,
  isAdmin,
} from '../helpers';

import { ITEMS_PER_PAGE } from '../constants';
import { renderGroup, renderQuota } from '../helpers/render';
import {
  Button,
  Divider,
  Dropdown,
  Form,
  Modal,
  Popconfirm,
  Popover,
  Space,
  SplitButtonGroup,
  Table,
  Tag,
  Input,
} from '@douyinfe/semi-ui';

import { IconTreeTriangleDown, IconKey, IconCopy } from '@douyinfe/semi-icons';
import EditToken from '../pages/Token/EditToken';
import { useTranslation } from 'react-i18next';

function renderTimestamp(timestamp) {
  return <>{timestamp2string(timestamp)}</>;
}

const TokensTable = () => {
  const { t } = useTranslation();

  const renderStatus = (status, model_limits_enabled = false) => {
    switch (status) {
      case 1:
        if (model_limits_enabled) {
          return (
            <Tag color='green' size='large'>
              {t('已启用：限制模型')}
            </Tag>
          );
        } else {
          return (
            <Tag color='green' size='large'>
              {t('已启用')}
            </Tag>
          );
        }
      case 2:
        return (
          <Tag color='red' size='large'>
            {t('已禁用')}
          </Tag>
        );
      case 3:
        return (
          <Tag color='yellow' size='large'>
            {t('已过期')}
          </Tag>
        );
      case 4:
        return (
          <Tag color='grey' size='large'>
            {t('已耗尽')}
          </Tag>
        );
      default:
        return (
          <Tag color='black' size='large'>
            {t('未知状态')}
          </Tag>
        );
    }
  };

  const columns = [
    {
      title: '',
      dataIndex: 'key',
      render: (text, record, index) => {
        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <IconKey size="large" style={{ fontSize: '24px', marginRight: '12px' }} />
            <Input type="text" value={'sk-' + record.key} readOnly style={{ width: '300px', marginRight: '12px' }} />
            <Button
              icon={<IconCopy />}
              theme='light'
              type='secondary'
              onClick={async () => {
                await copyText('sk-' + record.key);
              }}
              style={{ marginRight: '10px' }}
            >
              {t('复制')}
            </Button>
            <Popconfirm
              title={t('确定要删除吗？')}
              okText={t('确定')}
              cancelText={t('取消')}
              position="left"
              onConfirm={() => {
                manageToken(record.id, 'delete', record);
              }}
            >
              <Button type="danger" theme="light">
                {t('删除')}
              </Button>
            </Popconfirm>
          </div>
        );
      },
    }
  ];

  const [pageSize, setPageSize] = useState(ITEMS_PER_PAGE);
  const [showEdit, setShowEdit] = useState(false);
  const [tokens, setTokens] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [tokenCount, setTokenCount] = useState(pageSize);
  const [loading, setLoading] = useState(true);
  const [activePage, setActivePage] = useState(1);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [searchToken, setSearchToken] = useState('');
  const [searching, setSearching] = useState(false);
  const [editingToken, setEditingToken] = useState({
    id: undefined,
  });

  const closeEdit = () => {
    setShowEdit(false);
    setTimeout(() => {
      setEditingToken({
        id: undefined,
      });
    }, 500);
  };

  // 添加条件令牌函数
  const createConditionalToken = () => {
    setEditingToken({
      id: undefined,
    });
    setShowEdit(true);
  };

  // 添加自动创建令牌的函数
  const createAutoToken = async () => {
    setLoading(true);
    // 生成随机名称
    const randomName = `自动令牌-${Math.floor(Math.random() * 10000)}`;
    // 构造永不过期、无限额度的令牌数据
    const tokenData = {
      name: randomName,
      expired_time: -1, // 永不过期
      remain_quota: 0, // 实际不起作用，因为设置了无限额度
      unlimited_quota: true, // 无限额度
      model_limits_enabled: false,
      model_limits: '',
      allow_ips: '',
      group: 'default'
    };

    try {
      const res = await API.post('/api/token/', tokenData);
      const { success, message } = res.data;
      if (success) {
        showSuccess(t('令牌创建成功，请在列表中找到并复制！'));
        refresh(); // 刷新列表
      } else {
        showError(t(message));
      }
    } catch (error) {
      showError(t('创建令牌失败，请稍后再试'));
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const setTokensFormat = (tokens) => {
    setTokens(tokens);
    if (tokens.length >= pageSize) {
      setTokenCount(tokens.length + pageSize);
    } else {
      setTokenCount(tokens.length);
    }
  };

  let pageData = tokens.slice(
    (activePage - 1) * pageSize,
    activePage * pageSize,
  );
  const loadTokens = async (startIdx) => {
    setLoading(true);
    const res = await API.get(`/api/token/?p=${startIdx}&size=${pageSize}`);
    const { success, message, data } = res.data;
    if (success) {
      if (startIdx === 0) {
        setTokensFormat(data);
      } else {
        let newTokens = [...tokens];
        newTokens.splice(startIdx * pageSize, data.length, ...data);
        setTokensFormat(newTokens);
      }
    } else {
      showError(message);
    }
    setLoading(false);
  };

  const refresh = async () => {
    await loadTokens(activePage - 1);
  };

  const copyText = async (text) => {
    if (await copy(text)) {
      showSuccess(t('已复制到剪贴板！'));
    } else {
      Modal.error({
        title: t('无法复制到剪贴板，请手动复制'),
        content: text,
        size: 'large',
      });
    }
  };

  useEffect(() => {
    loadTokens(0)
      .then()
      .catch((reason) => {
        showError(reason);
      });
  }, [pageSize]);

  const removeRecord = (key) => {
    let newDataSource = [...tokens];
    if (key != null) {
      let idx = newDataSource.findIndex((data) => data.key === key);

      if (idx > -1) {
        newDataSource.splice(idx, 1);
        setTokensFormat(newDataSource);
      }
    }
  };

  const manageToken = async (id, action, record) => {
    setLoading(true);
    let data = { id };
    let res;
    switch (action) {
      case 'delete':
        res = await API.delete(`/api/token/${id}/`);
        break;
      case 'enable':
        data.status = 1;
        res = await API.put('/api/token/?status_only=true', data);
        break;
      case 'disable':
        data.status = 2;
        res = await API.put('/api/token/?status_only=true', data);
        break;
    }
    const { success, message } = res.data;
    if (success) {
      showSuccess('操作成功完成！');
      let token = res.data.data;
      let newTokens = [...tokens];
      // let realIdx = (activePage - 1) * ITEMS_PER_PAGE + idx;
      if (action === 'delete') {
        refresh(); // 删除成功后刷新列表
      } else {
        record.status = token.status;
        // newTokens[realIdx].status = token.status;
      }
      setTokensFormat(newTokens);
    } else {
      showError(message);
    }
    setLoading(false);
  };

  const searchTokens = async () => {
    if (searchKeyword === '' && searchToken === '') {
      // if keyword is blank, load files instead.
      await loadTokens(0);
      setActivePage(1);
      return;
    }
    setSearching(true);
    const res = await API.get(
      `/api/token/search?keyword=${searchKeyword}&token=${searchToken}`,
    );
    const { success, message, data } = res.data;
    if (success) {
      setTokensFormat(data);
      setActivePage(1);
    } else {
      showError(message);
    }
    setSearching(false);
  };

  const handleKeywordChange = async (value) => {
    setSearchKeyword(value.trim());
  };

  const handleSearchTokenChange = async (value) => {
    setSearchToken(value.trim());
  };

  const sortToken = (key) => {
    if (tokens.length === 0) return;
    setLoading(true);
    let sortedTokens = [...tokens];
    sortedTokens.sort((a, b) => {
      return ('' + a[key]).localeCompare(b[key]);
    });
    if (sortedTokens[0].id === tokens[0].id) {
      sortedTokens.reverse();
    }
    setTokens(sortedTokens);
    setLoading(false);
  };

  const handlePageChange = (page) => {
    setActivePage(page);
    if (page === Math.ceil(tokens.length / pageSize) + 1) {
      // In this case we have to load more data and then append them.
      loadTokens(page - 1).then((r) => {});
    }
  };

  const rowSelection = {
    onSelect: (record, selected) => {},
    onSelectAll: (selected, selectedRows) => {},
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedKeys(selectedRows);
    },
  };

  const handleRow = (record, index) => {
    if (record.status !== 1) {
      return {
        style: {
          background: 'var(--semi-color-disabled-border)',
        },
      };
    } else {
      return {};
    }
  };

  return (
    <>
      <EditToken
        refresh={refresh}
        editingToken={editingToken}
        visiable={showEdit}
        handleClose={closeEdit}
      ></EditToken>
      <Divider style={{ margin: '15px 0' }} />
      <div>
        <Button
          theme='light'
          type='primary'
          style={{ marginRight: 8 }}
          onClick={createAutoToken}
        >
          {t('添加令牌')}
        </Button>
        {isAdmin() && (
          <Button
            theme='light'
            type='primary'
            style={{ marginRight: 8 }}
            onClick={createConditionalToken}
          >
            {t('添加条件令牌')}
          </Button>
        )}
      </div>

      <Table
        style={{ marginTop: 20 }}
        columns={columns}
        dataSource={pageData}
        pagination={{
          currentPage: activePage,
          pageSize: pageSize,
          total: tokenCount,
          showSizeChanger: true,
          pageSizeOptions: [10, 20, 50, 100],
          formatPageText: (page) =>
            t('第 {{start}} - {{end}} 条，共 {{total}} 条', {
              start: page.currentStart,
              end: page.currentEnd,
              total: tokens.length,
            }),
          onPageSizeChange: (size) => {
            setPageSize(size);
            setActivePage(1);
          },
          onPageChange: handlePageChange,
        }}
        loading={loading}
        rowSelection={false}
        onRow={handleRow}
        showHeader={false}
      ></Table>
    </>
  );
};

export default TokensTable;
