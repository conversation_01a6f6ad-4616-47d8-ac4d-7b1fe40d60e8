import React, { useState, useEffect } from 'react';
import TokensTable from '../../components/TokensTable';
import { Banner, Layout, Space, Button, Spin, Typography } from '@douyinfe/semi-ui';
import { useTranslation } from 'react-i18next';
import { API, showError } from '../../helpers';
import { renderQuota, renderNumber } from '../../helpers/render';
import { IconHistogram, IconCreditCard, IconBookStroked } from '@douyinfe/semi-icons';
import { Link } from 'react-router-dom';

const Token = () => {
  const { t } = useTranslation();
  const [stat, setStat] = useState({ quota: 0, used_quota: 0, request_count: 0 });
  const [loading, setLoading] = useState(true);

  // 获取用户统计信息
  const getUserStat = async () => {
    try {
      setLoading(true);
      const res = await API.get('/api/user/self');
      const { success, message, data } = res.data;
      if (success) {
        setStat({
          quota: data.quota,
          used_quota: data.used_quota,
          request_count: data.request_count
        });
      } else {
        showError(message);
      }
    } catch (error) {
      console.error('获取用户统计信息失败', error);
      showError('获取用户统计信息失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getUserStat();
  }, []);

  return (
    <>
      <Layout>
        <Layout.Header>
          <Space vertical align="start" style={{ width: '100%' }}>
            
            <Spin spinning={loading}>
              <Space style={{ marginTop: '16px', marginBottom: '16px', display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
                <div
                  style={{
                    padding: '12px 24px',
                    borderRadius: '10px',
                    backgroundColor: 'rgba(var(--semi-green-0), 0.2)',
                    color: 'var(--semi-color-success)',
                    boxShadow: '0 2px 12px rgba(0, 0, 0, 0.05)',
                    fontSize: '16px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px',
                    border: '1px solid rgba(var(--semi-green-3), 0.2)',
                    minWidth: '200px',
                  }}
                >
                  <IconCreditCard size="large" style={{ color: 'var(--semi-color-success)', opacity: 0.8 }} />
                  <div>
                    <Typography.Text style={{ fontSize: '14px', opacity: 0.85 }}>{t('当前余额')}</Typography.Text>
                    <Typography.Text style={{ fontSize: '20px', fontWeight: 600, display: 'block' }}>{renderQuota(stat.quota)}</Typography.Text>
                  </div>
                </div>
                <Link to="/log">
                  <Button 
                    icon={<IconHistogram />} 
                    theme="solid" 
                    style={{
                      marginLeft: '16px',
                      borderRadius: '8px',
                      padding: '10px 16px',
                      height: 'auto',
                      fontWeight: 500,
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                      transition: 'transform 0.2s ease-in-out',
                    }}
                    onClick={(e) => {
                      e.currentTarget.style.transform = 'scale(0.98)';
                      setTimeout(() => {
                        e.currentTarget.style.transform = 'scale(1)';
                      }, 150);
                    }}
                  >
                    {t('查看消费明细')}
                  </Button>
                </Link>
                <Link to="/plugin-pricing">
                  <Button 
                    icon={<IconBookStroked />} 
                    theme="solid" 
                    type="warning"
                    style={{
                      marginLeft: '16px',
                      borderRadius: '8px',
                      padding: '10px 16px',
                      height: 'auto',
                      fontWeight: 500,
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                      transition: 'transform 0.2s ease-in-out',
                    }}
                    onClick={(e) => {
                      e.currentTarget.style.transform = 'scale(0.98)';
                      setTimeout(() => {
                        e.currentTarget.style.transform = 'scale(1)';
                      }, 150);
                    }}
                  >
                    {t('计费标准')}
                  </Button>
                </Link>
                <Link to="/topup">
                  <Button 
                    icon={<IconCreditCard />} 
                    theme="solid" 
                    type="tertiary"
                    style={{
                      marginLeft: '16px',
                      borderRadius: '8px',
                      padding: '10px 16px',
                      height: 'auto',
                      fontWeight: 500,
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                      transition: 'transform 0.2s ease-in-out',
                    }}
                    onClick={(e) => {
                      e.currentTarget.style.transform = 'scale(0.98)';
                      setTimeout(() => {
                        e.currentTarget.style.transform = 'scale(1)';
                      }, 150);
                    }}
                  >
                    {t('充值余额')}
                  </Button>
                </Link>
              </Space>
            </Spin>
          </Space>
        </Layout.Header>
        <Layout.Content>
          <TokensTable />
        </Layout.Content>
      </Layout>
    </>
  );
};

export default Token;
