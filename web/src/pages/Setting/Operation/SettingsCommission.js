import React, { useEffect, useState, useRef } from 'react';
import { Button, Form, InputNumber, Select, Tag, Spin } from '@douyinfe/semi-ui';
import {
  API,
  compareObjects,
  showError,
  showSuccess,
  showWarning,
} from '../../../helpers';

// 组件采用与其他设置组件一致的旧版 Option API 读写方式
const SettingsCommission = (props) => {
  const defaultConfig = {
    enabled: false,
    first_level_rate: 0.5,
    second_level_rate: 0.2,
    min_withdraw_amount: 10000,
    withdraw_enabled: true,
    auto_settle: true,
    promotion_whitelist: [],
    promotion_open_to_all: 'whitelist',
  };

  const [formData, setFormData] = useState(defaultConfig);
  const [formDataRow, setFormDataRow] = useState(defaultConfig);
  const [loading, setLoading] = useState(false);
  const refForm = useRef();

  // 当父组件 options 更新时，同步 CommissionConfig
  useEffect(() => {
    if (!props.options) return;
    const configStr = props.options['CommissionConfig'];
    if (!configStr) return; // 服务端可能尚未返回

    try {
      const parsed = JSON.parse(configStr);
      // 保障字段完整
      const merged = {
        ...defaultConfig,
        ...parsed,
        promotion_open_to_all: parsed.promotion_open_to_all ? 'all' : 'whitelist',
      };
      setFormData(merged);
      setFormDataRow(structuredClone(merged));
      // 设置表单初始值
      refForm.current && refForm.current.setValues(merged);
    } catch (e) {
      // JSON 解析失败则保持默认
      console.error('解析 CommissionConfig 失败: ', e);
    }
  }, [props.options]);

  const handleFormChange = (key, value) => {
    setFormData((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleWhitelistChange = (value) => {
    const list = value
      .split(',')
      .map((item) => item.trim())
      .filter((item) => item);
    handleFormChange('promotion_whitelist', list);
  };

  // 提交配置
  const handleSubmit = async () => {
    // 保存时需要将字符串值转换回布尔
    const configToSave = {
      ...formData,
      promotion_open_to_all: formData.promotion_open_to_all === 'all',
    };

    const diff = compareObjects(configToSave, {
      ...formDataRow,
      promotion_open_to_all: formDataRow.promotion_open_to_all === 'all',
    });
    if (!diff.length) return showWarning('你似乎并没有修改什么');

    setLoading(true);
    try {
      const res = await API.put('/api/option/', {
        key: 'CommissionConfig',
        value: JSON.stringify(configToSave),
      });
      const { success, message } = res.data;
      if (success) {
        showSuccess('分销配置更新成功');
        // 重新拉取配置
        props.refresh && props.refresh();
        // 更新原始数据
        setFormDataRow(structuredClone(configToSave));
      } else {
        showError(message || '更新失败');
      }
    } catch (err) {
      showError('更新失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Spin spinning={loading}>
      <Form
        layout='vertical'
        style={{ width: '100%' }}
        getFormApi={(api) => (refForm.current = api)}
        initValues={formData}
      >
        <Form.Section text='分销设置'>
          <Form.Switch
            field='enabled'
            label='启用分销功能'
            checked={formData.enabled}
            onChange={(checked) => handleFormChange('enabled', checked)}
          />

          <Form.InputNumber
            field='first_level_rate'
            label='一级分销比例'
            style={{ width: '100%' }}
            min={0}
            max={1}
            step={0.01}
            precision={2}
            value={formData.first_level_rate}
            onChange={(value) => handleFormChange('first_level_rate', value)}
            suffix='（0-1之间，如0.5表示50%）'
            disabled={!formData.enabled}
          />

          <Form.InputNumber
            field='second_level_rate'
            label='二级分销比例'
            style={{ width: '100%' }}
            min={0}
            max={1}
            step={0.01}
            precision={2}
            value={formData.second_level_rate}
            onChange={(value) => handleFormChange('second_level_rate', value)}
            suffix='（0-1之间，如0.2表示20%）'
            disabled={!formData.enabled}
          />

          <Form.InputNumber
            field='min_withdraw_amount'
            label='最小提现额度'
            style={{ width: '100%' }}
            min={0}
            value={formData.min_withdraw_amount}
            onChange={(value) => handleFormChange('min_withdraw_amount', value)}
            suffix='额度'
            disabled={!formData.enabled}
          />

          <Form.Switch
            field='withdraw_enabled'
            label='启用提现功能'
            checked={formData.withdraw_enabled}
            onChange={(checked) => handleFormChange('withdraw_enabled', checked)}
            disabled={!formData.enabled}
          />

          <Form.Switch
            field='auto_settle'
            label='自动结算佣金'
            checked={formData.auto_settle}
            onChange={(checked) => handleFormChange('auto_settle', checked)}
            disabled={!formData.enabled}
            extraText='开启后，佣金产生后立即变为已结算状态；关闭后，需要管理员手动结算'
          />

          <Form.Select
            field='promotion_open_to_all'
            label='推广页面权限'
            optionList={[
              { label: '对所有用户开放', value: 'all' },
              { label: '仅白名单用户可访问', value: 'whitelist' },
            ]}
            placeholder='请选择权限策略'
            disabled={!formData.enabled}
            onChange={(val) => handleFormChange('promotion_open_to_all', val)}
          >
          </Form.Select>

          {formData.promotion_open_to_all === 'whitelist' && (
            <Form.TextArea
              field='promotion_whitelist'
              label='推广页面白名单'
              placeholder='请输入用户名，多个用户名用逗号分隔'
              value={formData.promotion_whitelist.join(', ')}
              onChange={handleWhitelistChange}
              disabled={!formData.enabled}
              rows={3}
              extraText={
                <div style={{ marginTop: 8 }}>
                  {formData.promotion_whitelist.map((user, index) => (
                    <Tag key={index} style={{ marginRight: 8, marginBottom: 8 }}>
                      {user}
                    </Tag>
                  ))}
                </div>
              }
            />
          )}

          <Button type='primary' onClick={handleSubmit} loading={loading}>
            保存分销配置
          </Button>

          <div
            style={{
              marginTop: 20,
              padding: 16,
              backgroundColor: '#f5f5f5',
              borderRadius: 4,
            }}
          >
            <h4 style={{ marginTop: 0 }}>使用说明：</h4>
            <ol style={{ paddingLeft: 20, margin: 0 }}>
              <li>启用分销功能后，用户消费时会自动计算分成</li>
              <li>一级分销：直接邀请人获得的佣金比例</li>
              <li>二级分销：邀请人的邀请人获得的佣金比例</li>
              <li>用户可在个人中心查看推广数据和申请提现</li>
              <li>管理员可在用户管理中为特定用户设置 InviteeGroup，实现分组定价</li>
            </ol>
          </div>
        </Form.Section>
      </Form>
    </Spin>
  );
};

export default SettingsCommission;
