import React from 'react';
import ChannelsTable from '../../components/ChannelsTable';
import { Layout } from '@douyinfe/semi-ui';
import { useTranslation } from 'react-i18next';
import { isRoot } from '../../helpers/utils';

const File = () => {
  const { t } = useTranslation();
  
  // 只有超级管理员才能看到渠道管理内容
  if (!isRoot()) {
    return <></>;
  }
  
  return (
    <>
      <Layout>
        <Layout.Header>
          <h3>{t('管理渠道')}</h3>
        </Layout.Header>
        <Layout.Content>
          <ChannelsTable />
        </Layout.Content>
      </Layout>
    </>
  );
};

export default File;
