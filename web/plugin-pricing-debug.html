<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>插件计费标准</title>
  <link rel="stylesheet" href="https://unpkg.com/@douyinfe/semi-ui/dist/css/semi.min.css">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 1200px;
      margin: 20px auto;
      padding: 0 20px;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      padding: 24px;
      margin-bottom: 24px;
    }
    .card-title {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 8px;
      color: #1C1F23;
    }
    .card-description {
      font-size: 16px;
      color: #4E5969;
      margin-bottom: 24px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      border-radius: 8px;
      overflow: hidden;
    }
    th {
      background-color: #f0f2f5;
      text-align: left;
      padding: 12px 16px;
      font-weight: 600;
      color: #1C1F23;
    }
    td {
      padding: 12px 16px;
      border-bottom: 1px solid #e9e9e9;
    }
    tr:last-child td {
      border-bottom: none;
    }
    .plugin-name {
      font-weight: bold;
    }
    .plugin-name-en {
      font-size: 12px;
      color: #86909c;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="card">
      <h1 class="card-title">计费标准</h1>
      <p class="card-description">以下是各插件的计费标准，价格单位为人民币元</p>
      
      <table>
        <thead>
          <tr>
            <th style="width: 50%">插件名称</th>
            <th style="width: 20%">计量单位</th>
            <th style="width: 30%">价格 (元)</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>
              <div class="plugin-name">B站搜索</div>
              <div class="plugin-name-en">bilibili_search</div>
            </td>
            <td>次</td>
            <td>0.03</td>
          </tr>
          <tr>
            <td>
              <div class="plugin-name">小红书搜索</div>
              <div class="plugin-name-en">xhs_search</div>
            </td>
            <td>次</td>
            <td>0.03</td>
          </tr>
          <tr>
            <td>
              <div class="plugin-name">语音识别文案</div>
              <div class="plugin-name-en">get_video_content_from_download</div>
            </td>
            <td>次</td>
            <td>0.05</td>
          </tr>
          <tr>
            <td>
              <div class="plugin-name">抖音主页</div>
              <div class="plugin-name-en">dy_user_profile_vedio</div>
            </td>
            <td>次</td>
            <td>0.03</td>
          </tr>
          <tr>
            <td>
              <div class="plugin-name">抖音搜索</div>
              <div class="plugin-name-en">dy_search_video</div>
            </td>
            <td>次</td>
            <td>0.03</td>
          </tr>
          <tr>
            <td>
              <div class="plugin-name">全平台无水印下载视频、图集</div>
              <div class="plugin-name-en">parse_video</div>
            </td>
            <td>次</td>
            <td>0.01</td>
          </tr>
          <tr>
            <td>
              <div class="plugin-name">修复文案</div>
              <div class="plugin-name-en">fix_content</div>
            </td>
            <td>每千字</td>
            <td>0.05</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</body>
</html> 