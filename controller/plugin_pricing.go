package controller

import (
	"fmt"
	"one-api/common"
	"one-api/model"
	"one-api/setting/operation_setting"
	"strconv"

	"github.com/gin-gonic/gin"
)

// GetAllPluginPrices 获取所有插件价格
func GetAllPluginPrices(c *gin.Context) {
	prices, err := model.GetAllPluginPrices()
	if err != nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": err.<PERSON>rror(),
		})
		return
	}
	c.JSO<PERSON>(200, gin.H{
		"success": true,
		"message": "",
		"data":    prices,
	})
}

// GetPluginPriceById 根据ID获取插件价格
func GetPluginPriceById(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(400, gin.H{
			"success": false,
			"message": "无效的ID",
		})
		return
	}
	price, err := model.GetPluginPriceById(id)
	if err != nil {
		c.<PERSON>(500, gin.H{
			"success": false,
			"message": err.<PERSON>(),
		})
		return
	}
	c.<PERSON>SO<PERSON>(200, gin.H{
		"success": true,
		"message": "",
		"data":    price,
	})
}

// AddPluginPrice 添加插件价格
func AddPluginPrice(c *gin.Context) {
	price := model.PluginPrice{}
	err := c.ShouldBindJSON(&price)
	if err != nil {
		c.JSON(400, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	if price.Key == "" || price.NameEn == "" || price.NameZh == "" || price.Unit == "" || price.Price == "" {
		c.JSON(400, gin.H{
			"success": false,
			"message": "字段不能为空",
		})
		return
	}
	err = model.InsertPluginPrice(&price)
	if err != nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(200, gin.H{
		"success": true,
		"message": "插件价格添加成功",
	})
}

// UpdatePluginPrice 更新插件价格
func UpdatePluginPrice(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(400, gin.H{
			"success": false,
			"message": "无效的ID",
		})
		return
	}
	price := model.PluginPrice{}
	err = c.ShouldBindJSON(&price)
	if err != nil {
		c.JSON(400, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	price.Id = id
	err = model.UpdatePluginPrice(&price)
	if err != nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(200, gin.H{
		"success": true,
		"message": "插件价格更新成功",
	})
}

// DeletePluginPrice 删除插件价格
func DeletePluginPrice(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(400, gin.H{
			"success": false,
			"message": "无效的ID",
		})
		return
	}
	err = model.DeletePluginPrice(id)
	if err != nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(200, gin.H{
		"success": true,
		"message": "插件价格删除成功",
	})
}

// ExportPluginPrices 导出插件价格
func ExportPluginPrices(c *gin.Context) {
	jsonData, err := model.ExportPluginPrices()
	if err != nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(200, gin.H{
		"success": true,
		"message": "",
		"data":    jsonData,
	})
}

// ImportPluginPrices 导入插件价格
func ImportPluginPrices(c *gin.Context) {
	var requestBody struct {
		JsonData string `json:"json_data"`
	}
	err := c.ShouldBindJSON(&requestBody)
	if err != nil {
		c.JSON(400, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	err = model.ImportPluginPrices(requestBody.JsonData)
	if err != nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(200, gin.H{
		"success": true,
		"message": "插件价格导入成功",
	})
}

// VerifyPluginPrices 校验插件价格是否与模型实际价格一致
func VerifyPluginPrices(c *gin.Context) {
	// 获取所有插件价格
	prices, err := model.GetAllPluginPrices()
	if err != nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 获取所有可用模型
	enabledModels := model.GetEnabledModels()

	// 创建模型映射表，用于快速检查某个模型是否已有价格设置
	priceMap := make(map[string]model.PluginPrice)
	for _, price := range prices {
		priceMap[price.Key] = price
	}

	// 存储校验结果
	type VerifyResult struct {
		Key             string `json:"key"`
		Name            string `json:"name"`
		Unit            string `json:"unit"`              // 计量单位
		PluginPrice     string `json:"plugin_price"`      // 表中的价格
		PluginPriceDesc string `json:"plugin_price_desc"` // 价格描述
		TokenPrice      string `json:"token_price"`       // 按量收费的价格
		RealPrice       string `json:"real_price"`        // 模型真实价格
	}

	results := make([]VerifyResult, 0)

	// 首先添加所有已有价格的插件
	for _, price := range prices {
		// 获取模型的按量收费价格（如果存在）
		var tokenPrice string
		var realPrice string
		modelPrice, hasPriceConfig := operation_setting.GetModelPrice(price.Key, false)
		modelRatio, hasRatioConfig := operation_setting.GetModelRatio(price.Key)

		// 计算真实价格
		if hasPriceConfig {
			// 按单次请求收费的模型
			realPrice = fmt.Sprintf("%.2f元/次", modelPrice)
		} else if hasRatioConfig {
			// 按token收费的模型
			// 计算每1K tokens的价格，使用合理的单位
			// QuotaPerUnit是$0.002/1K tokens的500000倍
			pricePerToken := (1.0 / float64(common.QuotaPerUnit)) * 1000.0 * modelRatio
			if pricePerToken < 0.01 {
				realPrice = fmt.Sprintf("%.6f元/1K tokens", pricePerToken)
			} else {
				realPrice = fmt.Sprintf("%.4f元/1K tokens", pricePerToken)
			}
		} else {
			// 如果没有配置价格和倍率
			realPrice = "未设置"
		}

		// 只对没有设置价格但有设置倍率的模型显示"按量"价格
		// 对已经设置了价格(price.Price)的模型，不再显示额外的token价格
		if price.Price != "0" && price.Price != "" {
			// 已设置价格的模型，使用表格中的价格
			tokenPrice = ""
		} else if hasRatioConfig {
			// 按token收费的模型
			// 计算每1K tokens的价格，使用合理的单位
			// QuotaPerUnit是$0.002/1K tokens的500000倍
			pricePerToken := (1.0 / float64(common.QuotaPerUnit)) * 1000.0 * modelRatio
			if pricePerToken < 0.01 {
				tokenPrice = fmt.Sprintf("%.6f/1K tokens", pricePerToken)
			} else {
				tokenPrice = fmt.Sprintf("%.4f/1K tokens", pricePerToken)
			}
		} else if hasPriceConfig {
			// 按单次请求收费的模型
			tokenPrice = fmt.Sprintf("%.2f/次", modelPrice)
		} else {
			// 如果没有配置价格和倍率，则使用默认值"未设置"
			tokenPrice = ""
		}

		result := VerifyResult{
			Key:             price.Key,
			Name:            price.NameZh,
			Unit:            price.Unit,
			PluginPrice:     price.Price,
			PluginPriceDesc: price.PriceDesc,
			TokenPrice:      tokenPrice,
			RealPrice:       realPrice,
		}

		results = append(results, result)
	}

	// 然后添加所有未设置价格的模型
	for _, modelKey := range enabledModels {
		if _, exists := priceMap[modelKey]; !exists {
			// 获取模型的按量收费价格（如果存在）
			var tokenPrice string
			var realPrice string
			modelPrice, hasPriceConfig := operation_setting.GetModelPrice(modelKey, false)
			modelRatio, hasRatioConfig := operation_setting.GetModelRatio(modelKey)

			// 计算真实价格
			if hasPriceConfig {
				// 按单次请求收费的模型
				realPrice = fmt.Sprintf("%.2f元/次", modelPrice)
			} else if hasRatioConfig {
				// 按token收费的模型
				// 计算每1K tokens的价格，使用合理的单位
				// QuotaPerUnit是$0.002/1K tokens的500000倍
				pricePerToken := (1.0 / float64(common.QuotaPerUnit)) * 1000.0 * modelRatio
				if pricePerToken < 0.01 {
					realPrice = fmt.Sprintf("%.6f元/1K tokens", pricePerToken)
				} else {
					realPrice = fmt.Sprintf("%.4f元/1K tokens", pricePerToken)
				}
			} else {
				// 如果没有配置价格和倍率
				realPrice = "未设置"
			}

			if hasRatioConfig {
				// 按token收费的模型
				// 计算每1K tokens的价格，使用合理的单位
				// QuotaPerUnit是$0.002/1K tokens的500000倍
				pricePerToken := (1.0 / float64(common.QuotaPerUnit)) * 1000.0 * modelRatio
				if pricePerToken < 0.01 {
					tokenPrice = fmt.Sprintf("%.6f/1K tokens", pricePerToken)
				} else {
					tokenPrice = fmt.Sprintf("%.4f/1K tokens", pricePerToken)
				}
			} else if hasPriceConfig {
				// 按单次请求收费的模型
				tokenPrice = fmt.Sprintf("%.2f/次", modelPrice)
			} else {
				// 如果没有配置价格和倍率，则使用默认值"未设置"
				tokenPrice = "未设置"
			}

			// 这个模型没有价格设置
			result := VerifyResult{
				Key:             modelKey,
				Name:            modelKey, // 使用模型键作为名称，因为没有中文名
				Unit:            "未设置",
				PluginPrice:     "0",
				PluginPriceDesc: "未设置价格",
				TokenPrice:      tokenPrice,
				RealPrice:       realPrice,
			}
			results = append(results, result)
		}
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "插件价格校验完成",
		"data":    results,
	})
}
