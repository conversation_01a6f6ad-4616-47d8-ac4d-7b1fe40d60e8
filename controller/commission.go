package controller

import (
	"fmt"
	"net/http"
	"one-api/common"
	"one-api/model"
	"one-api/service"
	"one-api/setting"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// GetPromotionData 获取推广页面数据
func GetPromotionData(c *gin.Context) {
	userId := c.GetInt("id")
	username := c.GetString("username")

	// 检查用户是否有权限访问推广页面
	if !setting.IsUserAllowedPromotion(username) {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "您没有权限访问推广页面",
		})
		return
	}

	// 获取佣金统计
	statistics, err := service.GetCommissionStatistics(userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取佣金统计失败",
		})
		return
	}

	// 获取佣金明细统计
	details, err := service.GetCommissionDetails(userId)
	if err != nil {
		c.JSO<PERSON>(http.StatusOK, gin.H{
			"success": false,
			"message": "获取佣金明细失败",
		})
		return
	}

	// 获取用户的邀请码
	user, err := model.GetUserById(userId, false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取用户信息失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"statistics":       statistics,
			"details":          details,
			"aff_code":         user.AffCode,
			"min_withdraw":     setting.GetMinWithdrawAmount(),
			"withdraw_enabled": setting.IsWithdrawEnabled(),
		},
	})
}

// GetCommissionList 获取佣金明细列表
func GetCommissionList(c *gin.Context) {
	userId := c.GetInt("id")
	p, _ := strconv.Atoi(c.Query("p"))
	pageSize, _ := strconv.Atoi(c.Query("page_size"))

	if p < 1 {
		p = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	offset := (p - 1) * pageSize

	commissions, total, err := model.GetUserCommissions(userId, offset, pageSize)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取佣金记录失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"items":     commissions,
			"total":     total,
			"page":      p,
			"page_size": pageSize,
		},
	})
}

// CreateWithdrawal 创建提现申请
func CreateWithdrawal(c *gin.Context) {
	userId := c.GetInt("id")

	// 检查提现是否启用
	if !setting.IsWithdrawEnabled() {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "提现功能暂未开启",
		})
		return
	}

	var req struct {
		Amount          int    `json:"amount" binding:"required"`
		PaymentMethod   string `json:"payment_method" binding:"required"`
		PaymentAccount  string `json:"payment_account" binding:"required"`
		PaymentRealName string `json:"payment_real_name" binding:"required"`
		PaymentContact  string `json:"payment_contact"`
		Remark          string `json:"remark"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "参数错误",
		})
		return
	}

	// 检查最小提现金额
	minWithdraw := setting.GetMinWithdrawAmount()
	if req.Amount < minWithdraw {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": fmt.Sprintf("提现金额不能小于 %s", common.LogQuota(minWithdraw)),
		})
		return
	}

	// 防止提现金额为负数或过大
	if req.Amount <= 0 || req.Amount > ********* { // 1亿额度上限
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "提现金额无效",
		})
		return
	}

	// 使用事务处理提现申请
	err := model.DB.Transaction(func(tx *gorm.DB) error {
		// 为了防止并发问题，我们需要对用户的佣金记录加锁
		// 通过在用户表上加行锁来实现互斥
		var userLock model.User
		if err := tx.Set("gorm:query_option", "FOR UPDATE").
			Where("id = ?", userId).
			First(&userLock).Error; err != nil {
			return fmt.Errorf("获取用户信息失败")
		}

		// 1. 查询用户的已结算佣金总额（在获取用户锁后查询，确保数据一致性）
		var settledTotal int64
		err := tx.Model(&model.Commission{}).
			Where("user_id = ? AND status = ?", userId, "settled").
			Select("COALESCE(SUM(amount), 0)").
			Scan(&settledTotal).Error
		if err != nil {
			return err
		}

		// 2. 查询用户已申请提现的总额（包括待审核、已通过、已打款的）
		var withdrawnTotal int64
		err = tx.Model(&model.Withdrawal{}).
			Where("user_id = ? AND status IN ?", userId, []string{"pending", "approved", "paid"}).
			Select("COALESCE(SUM(amount), 0)").
			Scan(&withdrawnTotal).Error
		if err != nil {
			return err
		}

		// 3. 计算真实可提现余额
		availableAmount := int(settledTotal) - int(withdrawnTotal)
		if availableAmount < req.Amount {
			return fmt.Errorf("可提现余额不足，当前可提现：%s", common.LogQuota(availableAmount))
		}

		// 3.5 检查最近是否有相同金额的提现申请（防重复提交）
		var recentCount int64
		fiveMinutesAgo := time.Now().Unix() - 300 // 5分钟前
		err = tx.Model(&model.Withdrawal{}).
			Where("user_id = ? AND amount = ? AND created_at > ?", userId, req.Amount, fiveMinutesAgo).
			Count(&recentCount).Error
		if err != nil {
			return err
		}
		if recentCount > 0 {
			return fmt.Errorf("请勿重复提交提现申请，请稍后再试")
		}

		// 4. 生成唯一订单号
		orderNo := fmt.Sprintf("WD%d%d%s", userId, time.Now().Unix(), common.GetRandomString(6))

		// 5. 计算实际提现金额（额度转换为元）
		realAmount := float64(req.Amount) / common.QuotaPerUnit
		if common.DisplayInCurrencyEnabled {
			realAmount = float64(req.Amount)
		}

		// 6. 创建提现申请记录
		withdrawal := &model.Withdrawal{
			UserId:          userId,
			Amount:          req.Amount,
			RealAmount:      realAmount,
			Status:          "pending",
			PaymentMethod:   req.PaymentMethod,
			PaymentAccount:  req.PaymentAccount,
			PaymentRealName: req.PaymentRealName,
			PaymentContact:  req.PaymentContact,
			OrderNo:         orderNo,
			Remark:          req.Remark,
			CreatedAt:       time.Now().Unix(),
			UpdatedAt:       time.Now().Unix(),
		}

		if err := tx.Create(withdrawal).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "提现申请已提交，请等待审核",
	})
}

// GetWithdrawalList 获取提现记录列表
func GetWithdrawalList(c *gin.Context) {
	userId := c.GetInt("id")
	p, _ := strconv.Atoi(c.Query("p"))
	pageSize, _ := strconv.Atoi(c.Query("page_size"))

	if p < 1 {
		p = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	offset := (p - 1) * pageSize

	withdrawals, total, err := model.GetUserWithdrawals(userId, offset, pageSize)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取提现记录失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"items":     withdrawals,
			"total":     total,
			"page":      p,
			"page_size": pageSize,
		},
	})
}

// GetPendingWithdrawals 获取待审核的提现申请（管理员）
func GetPendingWithdrawals(c *gin.Context) {
	p, _ := strconv.Atoi(c.Query("p"))
	pageSize, _ := strconv.Atoi(c.Query("page_size"))

	if p < 1 {
		p = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	offset := (p - 1) * pageSize

	withdrawals, total, err := model.GetPendingWithdrawals(offset, pageSize)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取待审核提现申请失败",
		})
		return
	}

	// 获取用户信息
	for _, withdrawal := range withdrawals {
		user, _ := model.GetUserById(withdrawal.UserId, false)
		if user != nil {
			withdrawal.Remark = fmt.Sprintf("用户：%s，%s", user.Username, withdrawal.Remark)
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"items":     withdrawals,
			"total":     total,
			"page":      p,
			"page_size": pageSize,
		},
	})
}

// ReviewWithdrawal 审核提现申请（管理员）
func ReviewWithdrawal(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))

	var req struct {
		Action      string `json:"action" binding:"required"` // approve, reject, paid
		AdminRemark string `json:"admin_remark"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "参数错误",
		})
		return
	}

	var status string
	switch req.Action {
	case "approve":
		status = "approved"
	case "reject":
		status = "rejected"
	case "paid":
		status = "paid"
	default:
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的操作",
		})
		return
	}

	// 使用事务处理审核
	err := model.DB.Transaction(func(tx *gorm.DB) error {
		// 1. 查询并锁定提现记录
		var withdrawal model.Withdrawal
		if err := tx.Set("gorm:query_option", "FOR UPDATE").First(&withdrawal, id).Error; err != nil {
			return fmt.Errorf("提现申请不存在")
		}

		// 2. 检查状态是否允许操作
		if withdrawal.Status != "pending" && (req.Action == "approve" || req.Action == "reject") {
			return fmt.Errorf("该提现申请已处理，不能重复操作")
		}
		if withdrawal.Status != "approved" && req.Action == "paid" {
			return fmt.Errorf("只有已通过的提现申请才能标记为已打款")
		}

		// 3. 更新提现状态
		updates := map[string]interface{}{
			"status":       status,
			"admin_remark": req.AdminRemark,
			"updated_at":   common.GetTimestamp(),
		}
		if status == "approved" {
			updates["approved_at"] = common.GetTimestamp()
		} else if status == "paid" {
			updates["paid_at"] = common.GetTimestamp()

			// 打款成功后，需要将对应金额的佣金标记为已提现
			err := model.MarkCommissionsAsWithdrawn(tx, withdrawal.UserId, withdrawal.Amount)
			if err != nil {
				return fmt.Errorf("标记佣金为已提现失败: %v", err)
			}

			// 验证剩余的settled佣金是否足够支付其他待处理的提现申请
			var settledSum int64
			err = tx.Model(&model.Commission{}).
				Where("user_id = ? AND status = ?", withdrawal.UserId, "settled").
				Select("COALESCE(SUM(amount), 0)").
				Scan(&settledSum).Error
			if err != nil {
				return err
			}

			// 如果剩余的settled佣金不够支付其他待处理的提现申请，需要警告
			var pendingWithdrawSum int64
			err = tx.Model(&model.Withdrawal{}).
				Where("user_id = ? AND id != ? AND status IN ?", withdrawal.UserId, withdrawal.Id, []string{"pending", "approved"}).
				Select("COALESCE(SUM(amount), 0)").
				Scan(&pendingWithdrawSum).Error
			if err != nil {
				return err
			}

			if settledSum < pendingWithdrawSum {
				common.SysError(fmt.Sprintf("警告：用户%d的剩余可提现佣金(%d)小于待处理提现申请(%d)",
					withdrawal.UserId, settledSum, pendingWithdrawSum))
			}
		}

		if err := tx.Model(&withdrawal).Updates(updates).Error; err != nil {
			return err
		}

		// 4. 记录操作日志
		actionName := map[string]string{
			"approved": "审核通过",
			"rejected": "审核拒绝",
			"paid":     "已打款",
		}[status]

		model.RecordLog(withdrawal.UserId, model.LogTypeSystem,
			fmt.Sprintf("提现申请%s，订单号：%s，金额：%s，备注：%s",
				actionName, withdrawal.OrderNo, common.LogQuota(withdrawal.Amount), req.AdminRemark))

		return nil
	})

	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "操作成功",
	})
}

// GetCommissionConfig 获取分销配置（管理员）
func GetCommissionConfig(c *gin.Context) {
	config := setting.GetCommissionConfig()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    config,
	})
}

// UpdateCommissionConfig 更新分销配置（管理员）
func UpdateCommissionConfig(c *gin.Context) {
	var config setting.CommissionConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "参数错误",
		})
		return
	}

	if err := setting.UpdateCommissionConfig(config); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 持久化到数据库
	configJSON := setting.CommissionConfig2JSONString()
	if err := model.UpdateOption("CommissionConfig", configJSON); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "保存配置失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新成功",
	})
}
